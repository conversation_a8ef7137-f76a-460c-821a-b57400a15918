# 1.2 Subscription Engine Implementation Plan

## Executive Summary

This document provides a detailed implementation plan for the Subscription Engine within the MyTorra Core Service. The Subscription Engine is a generic, reusable system that manages subscription tiers, billing cycles, payment processing, usage tracking, and subscription lifecycle management for all platform services.

**Implementation Status:** 🔄 **PARTIALLY IMPLEMENTED**
- ✅ Domain Entity: Subscription entity with business logic
- ✅ Value Objects: SubscriptionTier with validation
- ✅ Repository Interface: SubscriptionRepository port defined
- ❌ Service Layer: SubscriptionService not implemented
- ❌ Repository Implementation: PostgreSQL repository missing
- ❌ HTTP Handlers: Subscription API endpoints missing
- ❌ Background Workers: Billing and renewal workers missing
- ❌ Database Migration: Subscription table not created

---

## Table of Contents

1. [Architecture Overview](#1-architecture-overview)
2. [Implementation Components](#2-implementation-components)
3. [Subscription Tiers & Plans](#3-subscription-tiers--plans)
4. [Implementation Tasks](#4-implementation-tasks)
5. [Database Schema](#5-database-schema)
6. [API Endpoints](#6-api-endpoints)
7. [Background Jobs](#7-background-jobs)
8. [Testing Strategy](#8-testing-strategy)

---

## 1. Architecture Overview

### 1.1 Core Responsibilities

The Subscription Engine manages:

1. **Generic Subscription Management**
   - Multi-entity subscription support (sellers, restaurants, drivers, etc.)
   - Flexible tier definitions (Basic, Professional, Premium, Enterprise)
   - Custom plan configurations per entity type

2. **Billing Cycle Management**
   - Monthly and annual billing cycles
   - Prorated billing for mid-cycle changes
   - Automatic renewal processing
   - Failed payment handling and retry logic

3. **Payment Gateway Abstraction**
   - Integration with multiple payment providers
   - Secure payment method storage
   - Automatic charge processing
   - Refund and chargeback handling

4. **Subscription Lifecycle**
   - Upgrade/downgrade logic with immediate or end-of-cycle changes
   - Trial period handling with automatic conversion
   - Subscription pause/resume functionality
   - Cancellation with retention strategies

5. **Usage Tracking & Limits**
   - Feature-based usage monitoring
   - Soft and hard limit enforcement
   - Usage-based billing calculations
   - Overage handling and notifications

6. **Business Intelligence**
   - Subscription analytics and reporting
   - Churn prediction and prevention
   - Revenue forecasting
   - Customer lifetime value calculations

### 1.2 Entity Types Supported

The system supports subscriptions for various entity types:

- **Sellers** (MyTorra & Torra Shop)
  - Basic: 5 listings, standard support
  - Professional: 50 listings, priority ranking, analytics
  - Premium: Unlimited listings, featured placement, marketing tools

- **Restaurants** (Torra Food)
  - Starter: 50 orders/month, 5km radius, 20% commission
  - Growth: 200 orders/month, 10km radius, 15% commission
  - Enterprise: Unlimited orders, 15km radius, 10% commission

- **Drivers** (Torra Cab & Delivery)
  - Basic: Standard commission rates
  - Professional: Reduced commission, priority dispatch
  - Premium: Lowest commission, exclusive zones

### 1.3 Integration Points

- **Payment Service**: For processing subscription charges
- **Notification Service**: For billing reminders and updates
- **Event System**: For subscription lifecycle events
- **Analytics Service**: For usage tracking and reporting
- **External Services**: Stripe, local payment providers

---

## 2. Implementation Components

### 2.1 Missing Components to Implement

#### A. Application Layer
- `SubscriptionService` - Core business logic
- `SubscriptionPlanService` - Plan management
- `BillingService` - Payment processing
- `UsageTrackingService` - Feature usage monitoring

#### B. Infrastructure Layer
- `SubscriptionRepository` - PostgreSQL implementation
- `SubscriptionPlanRepository` - Plan storage
- `UsageRepository` - Usage tracking storage

#### C. HTTP Layer
- `SubscriptionHandler` - REST API endpoints
- `SubscriptionPlanHandler` - Plan management API
- Request/Response DTOs

#### D. Background Workers
- `BillingWorker` - Automatic billing processing
- `RenewalWorker` - Subscription renewals
- `UsageWorker` - Usage limit monitoring
- `NotificationWorker` - Billing reminders

#### E. Database Schema
- `subscriptions` table (partially defined)
- `subscription_plans` table
- `subscription_usage` table
- `subscription_transactions` table

---

## 3. Subscription Tiers & Plans

### 3.1 Tier Definitions

```go
// Already implemented in valueobjects/subscription_tier.go
const (
    TierBasic        SubscriptionTier = "basic"
    TierProfessional SubscriptionTier = "professional" 
    TierPremium      SubscriptionTier = "premium"
    TierEnterprise   SubscriptionTier = "enterprise"
)
```

### 3.2 Plan Configuration Structure

```go
type SubscriptionPlan struct {
    ID           uuid.UUID
    EntityType   string // "seller", "restaurant", "driver"
    Tier         SubscriptionTier
    Name         string
    Description  string
    Price        Money
    BillingCycle BillingCycle
    
    // Features & Limits
    Features     map[string]interface{}
    UsageLimits  map[string]int
    
    // Trial & Discounts
    TrialDays    int
    DiscountRate *float64
    
    // Availability
    IsActive     bool
    ValidFrom    time.Time
    ValidUntil   *time.Time
    
    CreatedAt    time.Time
    UpdatedAt    time.Time
}
```

### 3.3 Example Plan Configurations

#### Seller Plans (Torra Shop)
```yaml
basic:
  price: 0.00
  features:
    max_listings: 5
    analytics: false
    priority_support: false
    featured_placement: false
    
professional:
  price: 25.00
  features:
    max_listings: 50
    analytics: true
    priority_support: true
    featured_placement: false
    email_marketing: 500
    
premium:
  price: 75.00
  features:
    max_listings: -1  # unlimited
    analytics: true
    priority_support: true
    featured_placement: true
    email_marketing: -1  # unlimited
    custom_storefront: true
```

---

## 4. Implementation Tasks

### Phase 1: Core Service Implementation (3 days)

#### Task 1.1: Subscription Service
- Create `SubscriptionService` with CRUD operations
- Implement subscription lifecycle methods
- Add usage tracking and limit enforcement
- Include upgrade/downgrade logic

#### Task 1.2: Subscription Repository
- Implement PostgreSQL repository
- Add complex queries for analytics
- Include transaction support for billing operations

#### Task 1.3: Database Migration
- Create subscription tables
- Add indexes for performance
- Include foreign key constraints

### Phase 2: HTTP API Implementation (2 days)

#### Task 2.1: Subscription Handler
- Implement REST endpoints
- Add request validation
- Include proper error handling with i18n

#### Task 2.2: DTOs and Validation
- Create request/response DTOs
- Add comprehensive validation rules
- Include API documentation

### Phase 3: Background Workers (2 days)

#### Task 3.1: Billing Worker
- Implement automatic billing processing
- Add failed payment retry logic
- Include dunning management

#### Task 3.2: Usage Monitoring
- Create usage tracking workers
- Add limit enforcement
- Include overage notifications

### Phase 4: Testing & Integration (2 days)

#### Task 4.1: Unit Tests
- Test all service methods
- Mock external dependencies
- Achieve >80% code coverage

#### Task 4.2: Integration Tests
- Test with real database
- Include payment provider integration
- Test background job processing

---

## 5. Database Schema

### 5.1 Subscriptions Table (Enhanced)

```sql
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    entity_id UUID NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    
    -- Plan details
    plan_id UUID REFERENCES subscription_plans(id),
    tier VARCHAR(20) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    
    -- Pricing
    price DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    billing_cycle VARCHAR(20) NOT NULL,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    
    -- Trial
    trial_ends_at TIMESTAMP,
    is_trialing BOOLEAN DEFAULT FALSE,
    
    -- Billing periods
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    next_billing_date TIMESTAMP,
    
    -- Usage tracking
    usage_limits JSONB DEFAULT '{}',
    current_usage JSONB DEFAULT '{}',
    
    -- Pause/Resume
    paused_at TIMESTAMP,
    resume_at TIMESTAMP,
    
    -- Cancellation
    canceled_at TIMESTAMP,
    cancel_reason TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    activated_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    CONSTRAINT valid_status CHECK (status IN ('active', 'trialing', 'paused', 'canceled', 'expired')),
    CONSTRAINT valid_billing_cycle CHECK (billing_cycle IN ('monthly', 'annual')),
    CONSTRAINT valid_tier CHECK (tier IN ('basic', 'professional', 'premium', 'enterprise'))
);
```

### 5.2 Additional Tables

```sql
-- Subscription Plans
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(50) NOT NULL,
    tier VARCHAR(20) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Pricing
    price DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    billing_cycle VARCHAR(20) NOT NULL,
    
    -- Features
    features JSONB DEFAULT '{}',
    usage_limits JSONB DEFAULT '{}',
    
    -- Trial & Discounts
    trial_days INT DEFAULT 0,
    discount_rate DECIMAL(5, 4),
    
    -- Availability
    is_active BOOLEAN DEFAULT TRUE,
    valid_from TIMESTAMP NOT NULL DEFAULT NOW(),
    valid_until TIMESTAMP,
    
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    UNIQUE(entity_type, tier, billing_cycle)
);

-- Usage Tracking
CREATE TABLE IF NOT EXISTS subscription_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID NOT NULL REFERENCES subscriptions(id),
    feature VARCHAR(100) NOT NULL,
    usage_count INT NOT NULL DEFAULT 0,
    limit_count INT,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    UNIQUE(subscription_id, feature, period_start)
);

-- Subscription Transactions
CREATE TABLE IF NOT EXISTS subscription_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID NOT NULL REFERENCES subscriptions(id),
    payment_id UUID REFERENCES payments(id),
    
    -- Transaction details
    type VARCHAR(50) NOT NULL, -- 'charge', 'refund', 'adjustment'
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    description TEXT,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    processed_at TIMESTAMP,
    failed_at TIMESTAMP,
    failure_reason TEXT,
    
    -- Billing period
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    CONSTRAINT valid_type CHECK (type IN ('charge', 'refund', 'adjustment')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'completed', 'failed', 'canceled'))
);
```

---

## 6. API Endpoints

### 6.1 Subscription Management

```
GET    /api/v1/subscriptions              # List user subscriptions
GET    /api/v1/subscriptions/{id}         # Get subscription details
POST   /api/v1/subscriptions              # Create subscription
PUT    /api/v1/subscriptions/{id}         # Update subscription
DELETE /api/v1/subscriptions/{id}         # Cancel subscription

PUT    /api/v1/subscriptions/{id}/upgrade   # Upgrade subscription
PUT    /api/v1/subscriptions/{id}/downgrade # Downgrade subscription
PUT    /api/v1/subscriptions/{id}/pause     # Pause subscription
PUT    /api/v1/subscriptions/{id}/resume    # Resume subscription

GET    /api/v1/subscriptions/{id}/usage     # Get usage statistics
GET    /api/v1/subscriptions/{id}/invoices  # Get billing history
```

### 6.2 Subscription Plans

```
GET    /api/v1/subscription-plans                    # List available plans
GET    /api/v1/subscription-plans/{entity_type}      # Plans for entity type
GET    /api/v1/subscription-plans/{id}               # Get plan details
```

### 6.3 Admin Endpoints

```
POST   /api/v1/admin/subscription-plans              # Create plan
PUT    /api/v1/admin/subscription-plans/{id}         # Update plan
DELETE /api/v1/admin/subscription-plans/{id}         # Deactivate plan

GET    /api/v1/admin/subscriptions                   # List all subscriptions
GET    /api/v1/admin/subscriptions/analytics         # Subscription analytics
```

---

## 7. Background Jobs

### 7.1 Billing Jobs

```go
// Daily billing job
func ProcessDailyBilling(ctx context.Context) error {
    // Find subscriptions due for billing
    // Process payments
    // Handle failed payments
    // Send notifications
}

// Retry failed payments
func RetryFailedPayments(ctx context.Context) error {
    // Find failed payments within retry window
    // Attempt payment processing
    // Update subscription status
    // Send notifications
}
```

### 7.2 Usage Monitoring Jobs

```go
// Hourly usage check
func MonitorUsageLimits(ctx context.Context) error {
    // Check subscriptions approaching limits
    // Send warning notifications
    // Enforce hard limits
}

// Daily usage reset
func ResetDailyUsage(ctx context.Context) error {
    // Reset daily usage counters
    // Archive usage data
}
```

---

## 8. Testing Strategy

### 8.1 Unit Tests
- Service method testing with mocks
- Domain entity business logic
- Repository operations
- Handler request/response processing

### 8.2 Integration Tests
- Database operations with test containers
- Payment provider integration
- Background job processing
- API endpoint testing

### 8.3 Performance Tests
- Subscription creation/update performance
- Usage tracking scalability
- Billing job processing time
- Database query optimization

---

## Next Steps

1. **Review existing implementation** - Verify current subscription entity and interfaces
2. **Implement missing services** - Start with SubscriptionService
3. **Create database migration** - Set up subscription tables
4. **Build HTTP handlers** - Implement REST API
5. **Add background workers** - Billing and usage monitoring
6. **Write comprehensive tests** - Unit and integration tests
7. **Performance optimization** - Database indexes and query optimization

This plan provides a complete roadmap for implementing the Subscription Engine as a core component of the MyTorra platform, following the established clean architecture patterns and integrating with existing shared packages.

---

## 9. Detailed Implementation Examples

### 9.1 Subscription Service Implementation

```go
// internal/application/services/subscription_service.go
package services

import (
    "context"
    "fmt"
    "time"

    "github.com/google/uuid"
    "github.com/paradoxe35/torra/packages/events"
    "github.com/paradoxe35/torra/packages/i18n"
    "github.com/paradoxe35/torra/packages/logger"
    "github.com/paradoxe35/torra/services/core/internal/application/ports"
    "github.com/paradoxe35/torra/services/core/internal/domain/entities"
    "github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

type SubscriptionService struct {
    subscriptionRepo ports.SubscriptionRepository
    planRepo         ports.SubscriptionPlanRepository
    paymentService   ports.PaymentService
    eventPublisher   ports.EventPublisher
    logger           logger.Interface
    translator       *i18n.Translator
}

func NewSubscriptionService(
    subscriptionRepo ports.SubscriptionRepository,
    planRepo ports.SubscriptionPlanRepository,
    paymentService ports.PaymentService,
    eventPublisher ports.EventPublisher,
    logger logger.Interface,
    translator *i18n.Translator,
) *SubscriptionService {
    return &SubscriptionService{
        subscriptionRepo: subscriptionRepo,
        planRepo:         planRepo,
        paymentService:   paymentService,
        eventPublisher:   eventPublisher,
        logger:           logger,
        translator:       translator,
    }
}

func (s *SubscriptionService) CreateSubscription(ctx context.Context, req CreateSubscriptionRequest) (*entities.Subscription, error) {
    // Validate plan exists and is active
    plan, err := s.planRepo.FindByID(ctx, req.PlanID)
    if err != nil {
        return nil, fmt.Errorf("plan not found: %w", err)
    }

    if !plan.IsActive {
        return nil, fmt.Errorf("plan is not active")
    }

    // Check for existing active subscription
    existing, _ := s.subscriptionRepo.FindActiveByUserAndEntity(ctx, req.UserID, req.EntityID)
    if existing != nil {
        return nil, fmt.Errorf("active subscription already exists")
    }

    // Create subscription entity
    subscription := &entities.Subscription{
        ID:         uuid.New(),
        UserID:     req.UserID,
        EntityID:   req.EntityID,
        EntityType: req.EntityType,
        Tier:       plan.Tier,
        PlanName:   plan.Name,
        Price:      plan.Price,
        BillingCycle: plan.BillingCycle,
        Status:     entities.SubscriptionStatusTrialing,
        UsageLimits: plan.UsageLimits,
        CurrentUsage: make(map[string]int),
        CreatedAt:  time.Now(),
        UpdatedAt:  time.Now(),
    }

    // Set trial period if applicable
    if plan.TrialDays > 0 {
        trialEnd := time.Now().AddDate(0, 0, plan.TrialDays)
        subscription.TrialEndsAt = &trialEnd
        subscription.IsTrialing = true
    } else {
        subscription.Status = entities.SubscriptionStatusActive
        now := time.Now()
        subscription.ActivatedAt = &now
    }

    // Set billing periods
    subscription.CurrentPeriodStart = time.Now()
    if plan.BillingCycle == entities.BillingCycleMonthly {
        subscription.CurrentPeriodEnd = time.Now().AddDate(0, 1, 0)
        if !subscription.IsTrialing {
            nextBilling := time.Now().AddDate(0, 1, 0)
            subscription.NextBillingDate = &nextBilling
        }
    } else {
        subscription.CurrentPeriodEnd = time.Now().AddDate(1, 0, 0)
        if !subscription.IsTrialing {
            nextBilling := time.Now().AddDate(1, 0, 0)
            subscription.NextBillingDate = &nextBilling
        }
    }

    // Save subscription
    if err := s.subscriptionRepo.Save(ctx, subscription); err != nil {
        return nil, fmt.Errorf("failed to save subscription: %w", err)
    }

    // Process initial payment if not in trial
    if !subscription.IsTrialing && subscription.Price.Amount > 0 {
        if err := s.processInitialPayment(ctx, subscription); err != nil {
            s.logger.Error("failed to process initial payment", "error", err, "subscription_id", subscription.ID)
            // Don't fail subscription creation, but mark for retry
        }
    }

    // Publish event
    event := events.SubscriptionCreatedEvent{
        BaseEvent: events.NewBaseEvent("subscription.created"),
        SubscriptionID: subscription.ID.String(),
        UserID:         subscription.UserID.String(),
        PlanID:         plan.ID.String(),
        PlanName:       plan.Name,
        StartDate:      subscription.CurrentPeriodStart,
        EndDate:        subscription.CurrentPeriodEnd,
        Amount:         subscription.Price.Amount,
        Currency:       subscription.Price.Currency,
    }

    if err := s.eventPublisher.PublishAsync(ctx, event); err != nil {
        s.logger.Error("failed to publish subscription created event", "error", err)
    }

    return subscription, nil
}

func (s *SubscriptionService) UpgradeSubscription(ctx context.Context, subscriptionID uuid.UUID, newPlanID uuid.UUID) error {
    subscription, err := s.subscriptionRepo.FindByID(ctx, subscriptionID)
    if err != nil {
        return fmt.Errorf("subscription not found: %w", err)
    }

    if !subscription.CanUpgrade() {
        return fmt.Errorf("subscription cannot be upgraded")
    }

    newPlan, err := s.planRepo.FindByID(ctx, newPlanID)
    if err != nil {
        return fmt.Errorf("new plan not found: %w", err)
    }

    // Validate upgrade path
    if newPlan.Tier.GetPriority() <= subscription.Tier.GetPriority() {
        return fmt.Errorf("new plan is not an upgrade")
    }

    // Calculate prorated amount
    proratedAmount := s.calculateProratedAmount(subscription, newPlan)

    // Process prorated payment if needed
    if proratedAmount > 0 {
        if err := s.processUpgradePayment(ctx, subscription, proratedAmount); err != nil {
            return fmt.Errorf("failed to process upgrade payment: %w", err)
        }
    }

    // Update subscription
    subscription.Tier = newPlan.Tier
    subscription.PlanName = newPlan.Name
    subscription.Price = newPlan.Price
    subscription.UsageLimits = newPlan.UsageLimits
    subscription.UpdatedAt = time.Now()

    return s.subscriptionRepo.Update(ctx, subscription)
}

func (s *SubscriptionService) TrackUsage(ctx context.Context, subscriptionID uuid.UUID, feature string, amount int) error {
    subscription, err := s.subscriptionRepo.FindByID(ctx, subscriptionID)
    if err != nil {
        return fmt.Errorf("subscription not found: %w", err)
    }

    if subscription.Status != entities.SubscriptionStatusActive {
        return fmt.Errorf("subscription is not active")
    }

    // Check if within limits
    if !subscription.IsWithinUsageLimits(feature) {
        return fmt.Errorf("usage limit exceeded for feature: %s", feature)
    }

    // Increment usage
    if subscription.CurrentUsage == nil {
        subscription.CurrentUsage = make(map[string]int)
    }
    subscription.CurrentUsage[feature] += amount
    subscription.UpdatedAt = time.Now()

    // Save updated usage
    if err := s.subscriptionRepo.Update(ctx, subscription); err != nil {
        return fmt.Errorf("failed to update usage: %w", err)
    }

    // Check if approaching limit and send warning
    if limit, hasLimit := subscription.UsageLimits[feature]; hasLimit {
        usage := subscription.CurrentUsage[feature]
        if float64(usage)/float64(limit) >= 0.8 { // 80% threshold
            s.sendUsageWarning(ctx, subscription, feature, usage, limit)
        }
    }

    return nil
}

func (s *SubscriptionService) processInitialPayment(ctx context.Context, subscription *entities.Subscription) error {
    // Implementation for processing initial payment
    // This would integrate with the payment service
    return nil
}

func (s *SubscriptionService) calculateProratedAmount(subscription *entities.Subscription, newPlan *entities.SubscriptionPlan) float64 {
    // Implementation for calculating prorated upgrade amount
    return 0.0
}

func (s *SubscriptionService) processUpgradePayment(ctx context.Context, subscription *entities.Subscription, amount float64) error {
    // Implementation for processing upgrade payment
    return nil
}

func (s *SubscriptionService) sendUsageWarning(ctx context.Context, subscription *entities.Subscription, feature string, usage, limit int) {
    // Implementation for sending usage warning notifications
}
```

### 9.2 Subscription Repository Implementation

```go
// internal/adapters/postgres/repositories/subscription_repository.go
package repositories

import (
    "context"
    "fmt"
    "time"

    "github.com/google/uuid"
    "github.com/paradoxe35/torra/packages/database"
    "github.com/paradoxe35/torra/packages/errors"
    "github.com/paradoxe35/torra/services/core/internal/adapters/postgres/models"
    "github.com/paradoxe35/torra/services/core/internal/application/ports"
    "github.com/paradoxe35/torra/services/core/internal/domain/entities"
)

type subscriptionRepository struct {
    db *database.DB
}

func NewSubscriptionRepository(db *database.DB) ports.SubscriptionRepository {
    return &subscriptionRepository{db: db}
}

const findSubscriptionByIDQuery = `
    SELECT id, user_id, entity_id, entity_type, tier, plan_name,
           price, currency, billing_cycle, status, trial_ends_at, is_trialing,
           current_period_start, current_period_end, next_billing_date,
           usage_limits, current_usage, paused_at, resume_at,
           canceled_at, cancel_reason, activated_at, created_at, updated_at
    FROM subscriptions
    WHERE id = $1
`

func (r *subscriptionRepository) FindByID(ctx context.Context, id uuid.UUID) (*entities.Subscription, error) {
    var model models.SubscriptionModel
    err := r.db.GetContext(ctx, &model, findSubscriptionByIDQuery, id)
    if err != nil {
        if database.IsNoRowsError(err) {
            return nil, errors.ErrNotFound
        }
        return nil, fmt.Errorf("failed to find subscription by id: %w", err)
    }

    return model.ToEntity()
}

const findActiveByUserAndEntityQuery = `
    SELECT id, user_id, entity_id, entity_type, tier, plan_name,
           price, currency, billing_cycle, status, trial_ends_at, is_trialing,
           current_period_start, current_period_end, next_billing_date,
           usage_limits, current_usage, paused_at, resume_at,
           canceled_at, cancel_reason, activated_at, created_at, updated_at
    FROM subscriptions
    WHERE user_id = $1 AND entity_id = $2 AND status IN ('active', 'trialing')
    ORDER BY created_at DESC
    LIMIT 1
`

func (r *subscriptionRepository) FindActiveByUserAndEntity(ctx context.Context, userID, entityID uuid.UUID) (*entities.Subscription, error) {
    var model models.SubscriptionModel
    err := r.db.GetContext(ctx, &model, findActiveByUserAndEntityQuery, userID, entityID)
    if err != nil {
        if database.IsNoRowsError(err) {
            return nil, errors.ErrNotFound
        }
        return nil, fmt.Errorf("failed to find active subscription: %w", err)
    }

    return model.ToEntity()
}

const saveSubscriptionQuery = `
    INSERT INTO subscriptions (
        id, user_id, entity_id, entity_type, tier, plan_name,
        price, currency, billing_cycle, status, trial_ends_at, is_trialing,
        current_period_start, current_period_end, next_billing_date,
        usage_limits, current_usage, paused_at, resume_at,
        canceled_at, cancel_reason, activated_at, created_at, updated_at
    ) VALUES (
        :id, :user_id, :entity_id, :entity_type, :tier, :plan_name,
        :price, :currency, :billing_cycle, :status, :trial_ends_at, :is_trialing,
        :current_period_start, :current_period_end, :next_billing_date,
        :usage_limits, :current_usage, :paused_at, :resume_at,
        :canceled_at, :cancel_reason, :activated_at, :created_at, :updated_at
    )
`

func (r *subscriptionRepository) Save(ctx context.Context, subscription *entities.Subscription) error {
    model := models.NewSubscriptionModelFromEntity(subscription)
    _, err := r.db.NamedExecContext(ctx, saveSubscriptionQuery, model)
    if err != nil {
        return fmt.Errorf("failed to save subscription: %w", err)
    }
    return nil
}

const updateSubscriptionQuery = `
    UPDATE subscriptions SET
        tier = :tier,
        plan_name = :plan_name,
        price = :price,
        currency = :currency,
        billing_cycle = :billing_cycle,
        status = :status,
        trial_ends_at = :trial_ends_at,
        is_trialing = :is_trialing,
        current_period_start = :current_period_start,
        current_period_end = :current_period_end,
        next_billing_date = :next_billing_date,
        usage_limits = :usage_limits,
        current_usage = :current_usage,
        paused_at = :paused_at,
        resume_at = :resume_at,
        canceled_at = :canceled_at,
        cancel_reason = :cancel_reason,
        activated_at = :activated_at,
        updated_at = :updated_at
    WHERE id = :id
`

func (r *subscriptionRepository) Update(ctx context.Context, subscription *entities.Subscription) error {
    subscription.UpdatedAt = time.Now()
    model := models.NewSubscriptionModelFromEntity(subscription)
    _, err := r.db.NamedExecContext(ctx, updateSubscriptionQuery, model)
    if err != nil {
        return fmt.Errorf("failed to update subscription: %w", err)
    }
    return nil
}

const findByUserIDQuery = `
    SELECT id, user_id, entity_id, entity_type, tier, plan_name,
           price, currency, billing_cycle, status, trial_ends_at, is_trialing,
           current_period_start, current_period_end, next_billing_date,
           usage_limits, current_usage, paused_at, resume_at,
           canceled_at, cancel_reason, activated_at, created_at, updated_at
    FROM subscriptions
    WHERE user_id = $1
    ORDER BY created_at DESC
    LIMIT $2 OFFSET $3
`

func (r *subscriptionRepository) FindByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*entities.Subscription, error) {
    var models []models.SubscriptionModel
    err := r.db.SelectContext(ctx, &models, findByUserIDQuery, userID, limit, offset)
    if err != nil {
        return nil, fmt.Errorf("failed to find subscriptions by user id: %w", err)
    }

    subscriptions := make([]*entities.Subscription, len(models))
    for i, model := range models {
        subscription, err := model.ToEntity()
        if err != nil {
            return nil, fmt.Errorf("failed to convert model to entity: %w", err)
        }
        subscriptions[i] = subscription
    }

    return subscriptions, nil
}
```

### 9.3 Subscription Handler Implementation

```go
// internal/adapters/http/handlers/subscription_handler.go
package handlers

import (
    "encoding/json"
    "net/http"
    "strconv"

    "github.com/go-chi/chi/v5"
    "github.com/google/uuid"

    httpPkg "github.com/paradoxe35/torra/packages/http"
    "github.com/paradoxe35/torra/packages/i18n"
    "github.com/paradoxe35/torra/packages/logger"
    "github.com/paradoxe35/torra/services/core/internal/adapters/http/middleware"
    "github.com/paradoxe35/torra/services/core/internal/application/dtos"
    "github.com/paradoxe35/torra/services/core/internal/application/services"
)

type SubscriptionHandler struct {
    subscriptionService *services.SubscriptionService
    logger              logger.Interface
    translator          *i18n.Translator
}

func NewSubscriptionHandler(
    subscriptionService *services.SubscriptionService,
    logger logger.Interface,
    translator *i18n.Translator,
) *SubscriptionHandler {
    return &SubscriptionHandler{
        subscriptionService: subscriptionService,
        logger:              logger,
        translator:          translator,
    }
}

func (h *SubscriptionHandler) CreateSubscription(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    userIDStr := ctx.Value(middleware.UserIDKey).(string)
    userID, err := uuid.Parse(userIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_USER_ID",
            h.translator.TWithContext(ctx, "error.invalid_user_id"))
        return
    }

    var req dtos.CreateSubscriptionRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_REQUEST",
            h.translator.TWithContext(ctx, "error.invalid_request_body"))
        return
    }

    req.UserID = userID
    if err := req.Validate(ctx, h.translator); err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "VALIDATION_FAILED", err.Error())
        return
    }

    subscription, err := h.subscriptionService.CreateSubscription(ctx, req)
    if err != nil {
        h.logger.WithError(err).Error("Failed to create subscription")
        httpPkg.RespondError(w, http.StatusBadRequest, "SUBSCRIPTION_CREATION_FAILED",
            h.translator.TWithContext(ctx, "subscription.creation_failed"))
        return
    }

    httpPkg.RespondJSON(w, http.StatusCreated, subscription)
}

func (h *SubscriptionHandler) GetSubscription(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    subscriptionIDStr := chi.URLParam(r, "id")
    subscriptionID, err := uuid.Parse(subscriptionIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_SUBSCRIPTION_ID",
            h.translator.TWithContext(ctx, "error.invalid_subscription_id"))
        return
    }

    subscription, err := h.subscriptionService.GetSubscription(ctx, subscriptionID)
    if err != nil {
        h.logger.WithError(err).Error("Failed to get subscription")
        httpPkg.RespondError(w, http.StatusNotFound, "SUBSCRIPTION_NOT_FOUND",
            h.translator.TWithContext(ctx, "subscription.not_found"))
        return
    }

    httpPkg.RespondJSON(w, http.StatusOK, subscription)
}

func (h *SubscriptionHandler) ListSubscriptions(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    userIDStr := ctx.Value(middleware.UserIDKey).(string)
    userID, err := uuid.Parse(userIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_USER_ID",
            h.translator.TWithContext(ctx, "error.invalid_user_id"))
        return
    }

    // Parse pagination parameters
    limitStr := r.URL.Query().Get("limit")
    offsetStr := r.URL.Query().Get("offset")

    limit := 10 // default
    if limitStr != "" {
        if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
            limit = l
        }
    }

    offset := 0 // default
    if offsetStr != "" {
        if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
            offset = o
        }
    }

    subscriptions, err := h.subscriptionService.ListUserSubscriptions(ctx, userID, limit, offset)
    if err != nil {
        h.logger.WithError(err).Error("Failed to list subscriptions")
        httpPkg.RespondError(w, http.StatusInternalServerError, "SUBSCRIPTION_LIST_FAILED",
            h.translator.TWithContext(ctx, "subscription.list_failed"))
        return
    }

    response := dtos.ListSubscriptionsResponse{
        Subscriptions: subscriptions,
        Limit:         limit,
        Offset:        offset,
        Total:         len(subscriptions), // This should be actual total count
    }

    httpPkg.RespondJSON(w, http.StatusOK, response)
}

func (h *SubscriptionHandler) UpgradeSubscription(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    subscriptionIDStr := chi.URLParam(r, "id")
    subscriptionID, err := uuid.Parse(subscriptionIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_SUBSCRIPTION_ID",
            h.translator.TWithContext(ctx, "error.invalid_subscription_id"))
        return
    }

    var req dtos.UpgradeSubscriptionRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_REQUEST",
            h.translator.TWithContext(ctx, "error.invalid_request_body"))
        return
    }

    if err := req.Validate(ctx, h.translator); err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "VALIDATION_FAILED", err.Error())
        return
    }

    err = h.subscriptionService.UpgradeSubscription(ctx, subscriptionID, req.NewPlanID)
    if err != nil {
        h.logger.WithError(err).Error("Failed to upgrade subscription")
        httpPkg.RespondError(w, http.StatusBadRequest, "SUBSCRIPTION_UPGRADE_FAILED",
            h.translator.TWithContext(ctx, "subscription.upgrade_failed"))
        return
    }

    httpPkg.RespondJSON(w, http.StatusOK, map[string]string{
        "message": h.translator.TWithContext(ctx, "subscription.upgraded_successfully"),
    })
}

func (h *SubscriptionHandler) PauseSubscription(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    subscriptionIDStr := chi.URLParam(r, "id")
    subscriptionID, err := uuid.Parse(subscriptionIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_SUBSCRIPTION_ID",
            h.translator.TWithContext(ctx, "error.invalid_subscription_id"))
        return
    }

    err = h.subscriptionService.PauseSubscription(ctx, subscriptionID)
    if err != nil {
        h.logger.WithError(err).Error("Failed to pause subscription")
        httpPkg.RespondError(w, http.StatusBadRequest, "SUBSCRIPTION_PAUSE_FAILED",
            h.translator.TWithContext(ctx, "subscription.pause_failed"))
        return
    }

    httpPkg.RespondJSON(w, http.StatusOK, map[string]string{
        "message": h.translator.TWithContext(ctx, "subscription.paused_successfully"),
    })
}

func (h *SubscriptionHandler) ResumeSubscription(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    subscriptionIDStr := chi.URLParam(r, "id")
    subscriptionID, err := uuid.Parse(subscriptionIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_SUBSCRIPTION_ID",
            h.translator.TWithContext(ctx, "error.invalid_subscription_id"))
        return
    }

    err = h.subscriptionService.ResumeSubscription(ctx, subscriptionID)
    if err != nil {
        h.logger.WithError(err).Error("Failed to resume subscription")
        httpPkg.RespondError(w, http.StatusBadRequest, "SUBSCRIPTION_RESUME_FAILED",
            h.translator.TWithContext(ctx, "subscription.resume_failed"))
        return
    }

    httpPkg.RespondJSON(w, http.StatusOK, map[string]string{
        "message": h.translator.TWithContext(ctx, "subscription.resumed_successfully"),
    })
}

func (h *SubscriptionHandler) CancelSubscription(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    subscriptionIDStr := chi.URLParam(r, "id")
    subscriptionID, err := uuid.Parse(subscriptionIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_SUBSCRIPTION_ID",
            h.translator.TWithContext(ctx, "error.invalid_subscription_id"))
        return
    }

    var req dtos.CancelSubscriptionRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        // Reason is optional, so empty body is OK
        req.Reason = ""
    }

    err = h.subscriptionService.CancelSubscription(ctx, subscriptionID, req.Reason)
    if err != nil {
        h.logger.WithError(err).Error("Failed to cancel subscription")
        httpPkg.RespondError(w, http.StatusBadRequest, "SUBSCRIPTION_CANCEL_FAILED",
            h.translator.TWithContext(ctx, "subscription.cancel_failed"))
        return
    }

    httpPkg.RespondJSON(w, http.StatusOK, map[string]string{
        "message": h.translator.TWithContext(ctx, "subscription.canceled_successfully"),
    })
}

func (h *SubscriptionHandler) GetUsage(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    subscriptionIDStr := chi.URLParam(r, "id")
    subscriptionID, err := uuid.Parse(subscriptionIDStr)
    if err != nil {
        httpPkg.RespondError(w, http.StatusBadRequest, "INVALID_SUBSCRIPTION_ID",
            h.translator.TWithContext(ctx, "error.invalid_subscription_id"))
        return
    }

    usage, err := h.subscriptionService.GetUsageStatistics(ctx, subscriptionID)
    if err != nil {
        h.logger.WithError(err).Error("Failed to get usage statistics")
        httpPkg.RespondError(w, http.StatusNotFound, "USAGE_NOT_FOUND",
            h.translator.TWithContext(ctx, "subscription.usage_not_found"))
        return
    }

    httpPkg.RespondJSON(w, http.StatusOK, usage)
}
```

This comprehensive implementation plan provides all the necessary components to build a robust Subscription Engine for the MyTorra platform, following clean architecture principles and integrating seamlessly with the existing core service infrastructure.
